<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOT Editor | Ctx | Gemini-CLI Dashboard</title>
    <link rel="stylesheet" href="https://unpkg.com/open-props@1.7.4/open-props.min.css">
    <link rel="stylesheet" href="https://unpkg.com/open-props@1.7.4/normalize.min.css">
    <link rel="stylesheet" href="style.css">
    
    <!-- GraphViz WASM Library -->
    <script src="https://unpkg.com/@hpcc-js/wasm@2.13.0/dist/graphviz.umd.js"></script>
    
    <style>
      /* Always show vertical scrollbar to prevent layout jank */
      html {
        overflow-y: scroll;
      }
      
      /* DOT Editor specific styles */
      .editor-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: var(--size-6);
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .editor-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--size-6);
        margin-bottom: var(--size-6);
      }
      
      .editor-panel {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        padding: var(--size-4);
        box-shadow: var(--shadow);
      }
      
      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--size-4);
        padding-bottom: var(--size-2);
        border-bottom: 1px solid var(--border-color);
      }
      
      .panel-title {
        font-size: var(--font-size-4);
        font-weight: var(--font-weight-6);
        margin: 0;
        color: var(--text-color);
      }
      
      .dot-textarea {
        width: 100%;
        height: 400px;
        font-family: 'Courier New', monospace;
        font-size: var(--font-size-2);
        line-height: var(--font-lineheight-3);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        padding: var(--size-3);
        background: var(--gray-0);
        color: var(--gray-9);
        resize: vertical;
        outline: none;
        transition: border-color 0.2s ease;
      }
      
      .dot-textarea:focus {
        border-color: var(--blue-7);
        box-shadow: 0 0 0 2px var(--blue-2);
      }
      
      .preview-container {
        width: 100%;
        height: 400px;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-0);
        overflow: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }
      
      .preview-placeholder {
        color: var(--text-light);
        font-style: italic;
        text-align: center;
      }
      
      .preview-svg {
        max-width: 100%;
        max-height: 100%;
      }
      
      .controls-panel {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        padding: var(--size-4);
        box-shadow: var(--shadow);
      }
      
      .controls-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--size-4);
        margin-bottom: var(--size-4);
      }
      
      .form-group {
        display: flex;
        flex-direction: column;
        gap: var(--size-1);
      }
      
      .form-label {
        font-weight: var(--font-weight-6);
        color: var(--text-color);
        font-size: var(--font-size-1);
      }
      
      .form-input {
        padding: var(--size-2);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-0);
        color: var(--gray-9);
        font-size: var(--font-size-2);
        outline: none;
        transition: border-color 0.2s ease;
      }
      
      .form-input:focus {
        border-color: var(--blue-7);
        box-shadow: 0 0 0 2px var(--blue-2);
      }
      
      .form-select {
        padding: var(--size-2);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-0);
        color: var(--gray-9);
        font-size: var(--font-size-2);
        outline: none;
        cursor: pointer;
      }
      
      .button-group {
        display: flex;
        gap: var(--size-3);
        flex-wrap: wrap;
      }
      
      .btn {
        padding: var(--size-2) var(--size-4);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-1);
        color: var(--gray-9);
        font-size: var(--font-size-2);
        font-weight: var(--font-weight-5);
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--size-1);
      }
      
      .btn:hover {
        background: var(--gray-2);
        border-color: var(--gray-6);
      }
      
      .btn-primary {
        background: var(--blue-7);
        color: var(--gray-0);
        border-color: var(--blue-8);
      }
      
      .btn-primary:hover {
        background: var(--blue-8);
        border-color: var(--blue-9);
      }
      
      .btn-success {
        background: var(--green-7);
        color: var(--gray-0);
        border-color: var(--green-8);
      }
      
      .btn-success:hover {
        background: var(--green-8);
        border-color: var(--green-9);
      }
      
      .error-message {
        background: var(--red-1);
        border: 1px solid var(--red-4);
        border-radius: var(--radius-1);
        padding: var(--size-3);
        color: var(--red-9);
        margin-top: var(--size-3);
        font-size: var(--font-size-1);
      }
      
      .success-message {
        background: var(--green-1);
        border: 1px solid var(--green-4);
        border-radius: var(--radius-1);
        padding: var(--size-3);
        color: var(--green-9);
        margin-top: var(--size-3);
        font-size: var(--font-size-1);
      }
      
      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
        .dot-textarea {
          background: var(--gray-8);
          color: var(--gray-1);
        }
        
        .preview-container {
          background: var(--gray-8);
        }
        
        .form-input, .form-select {
          background: var(--gray-8);
          color: var(--gray-1);
        }
      }
      
      /* Modal styles */
      .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
      }

      .modal-content {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        padding: var(--size-6);
        max-width: 800px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: var(--shadow-4);
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--size-4);
        padding-bottom: var(--size-3);
        border-bottom: 1px solid var(--border-color);
      }

      .modal-header h3 {
        margin: 0;
        font-size: var(--font-size-4);
        color: var(--text-color);
      }

      .templates-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--size-4);
      }

      .template-card {
        background: var(--gray-0);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        padding: var(--size-3);
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .template-card:hover {
        border-color: var(--blue-6);
        background: var(--blue-1);
      }

      .template-title {
        font-weight: var(--font-weight-6);
        margin-bottom: var(--size-2);
        color: var(--text-color);
      }

      .template-description {
        font-size: var(--font-size-1);
        color: var(--text-light);
        margin-bottom: var(--size-3);
      }

      .template-preview {
        font-family: 'Courier New', monospace;
        font-size: var(--font-size-0);
        background: var(--gray-1);
        padding: var(--size-2);
        border-radius: var(--radius-1);
        color: var(--gray-8);
        white-space: pre-wrap;
        max-height: 100px;
        overflow: hidden;
      }

      /* Accessibility styles */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      }

      /* Focus indicators */
      button:focus,
      select:focus,
      textarea:focus,
      input:focus {
        outline: 2px solid var(--blue-7);
        outline-offset: 2px;
      }

      /* High contrast mode support */
      @media (prefers-contrast: high) {
        .btn {
          border-width: 2px;
        }

        .graph-card {
          border-width: 2px;
        }
      }

      /* Reduced motion support */
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }

      /* Dark theme support */
      @media (prefers-color-scheme: dark) {
        :root {
          --gray-0: var(--gray-12);
          --gray-1: var(--gray-11);
          --gray-2: var(--gray-10);
          --gray-3: var(--gray-9);
          --gray-4: var(--gray-8);
          --gray-5: var(--gray-7);
          --gray-6: var(--gray-6);
          --gray-7: var(--gray-5);
          --gray-8: var(--gray-4);
          --gray-9: var(--gray-3);
          --gray-10: var(--gray-2);
          --gray-11: var(--gray-1);
          --gray-12: var(--gray-0);
        }

        body {
          background: var(--gray-12);
          color: var(--gray-0);
        }

        .dot-textarea,
        input[type="text"],
        select {
          background: var(--gray-11);
          color: var(--gray-0);
          border-color: var(--gray-8);
        }

        .btn {
          background: var(--gray-10);
          color: var(--gray-0);
          border-color: var(--gray-8);
        }

        .btn:hover {
          background: var(--gray-9);
        }

        .btn.primary {
          background: var(--blue-9);
          color: var(--gray-0);
        }

        .btn.primary:hover {
          background: var(--blue-8);
        }

        .preview-container {
          background: var(--gray-11);
          border-color: var(--gray-8);
        }

        .modal-content {
          background: var(--gray-11);
          border-color: var(--gray-8);
        }

        .template-card {
          background: var(--gray-10);
          border-color: var(--gray-8);
        }

        .template-card:hover {
          background: var(--gray-9);
        }

        .template-preview {
          background: var(--gray-12);
          color: var(--gray-2);
        }

        .message {
          background: var(--gray-10);
          border-color: var(--gray-8);
        }

        .message.success {
          background: var(--green-11);
          color: var(--green-2);
          border-color: var(--green-8);
        }

        .message.error {
          background: var(--red-11);
          color: var(--red-2);
          border-color: var(--red-8);
        }

        .message.info {
          background: var(--blue-11);
          color: var(--blue-2);
          border-color: var(--blue-8);
        }
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .editor-layout {
          grid-template-columns: 1fr;
        }

        .controls-grid {
          grid-template-columns: 1fr;
        }

        .button-group {
          justify-content: center;
        }

        .modal-content {
          margin: var(--size-4);
          max-width: calc(100% - var(--size-8));
        }

        .templates-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="header-content">
        <div class="header-left">
          <h1 class="site-title">
            <a href="index.html">Ctx | Gemini-CLI Dashboard</a>
          </h1>
          <span class="page-subtitle">DOT Graph Editor</span>
        </div>
        <nav class="header-nav">
          <a href="index.html" class="nav-link">📄 Documents</a>
          <a href="dot-gallery.html" class="nav-link">📊 Graph Gallery</a>
          <a href="dot-editor.html" class="nav-link active">✏️ DOT Editor</a>
        </nav>
      </div>
    </header>

    <main class="editor-container">
      <div class="editor-layout">
        <!-- DOT Code Editor -->
        <div class="editor-panel">
          <div class="panel-header">
            <h2 class="panel-title">DOT Code</h2>
            <select id="engine-select" class="form-select">
              <option value="dot">dot (hierarchical)</option>
              <option value="neato">neato (spring model)</option>
              <option value="fdp">fdp (force-directed)</option>
              <option value="circo">circo (circular)</option>
              <option value="twopi">twopi (radial)</option>
            </select>
          </div>
          <textarea
            id="dot-input"
            class="dot-textarea"
            placeholder="Enter your DOT code here...

Example:
digraph G {
  A -> B;
  B -> C;
  C -> A;
}"
            aria-label="DOT code editor"
            aria-describedby="editor-help"
            spellcheck="false"
          ></textarea>
          <div id="editor-help" class="sr-only">
            Use Ctrl+Enter to render graph, Ctrl+S to save, Escape to close modals
          </div>
        </div>

        <!-- Live Preview -->
        <div class="editor-panel">
          <div class="panel-header">
            <h2 class="panel-title">Live Preview</h2>
            <button id="render-btn" class="btn btn-primary">🔄 Render</button>
          </div>
          <div id="preview-container" class="preview-container">
            <div class="preview-placeholder">
              Enter DOT code and click "Render" to see the graph
            </div>
          </div>
        </div>
      </div>

      <!-- Graph Metadata & Controls -->
      <div class="controls-panel">
        <h3 class="panel-title">Graph Metadata & Actions</h3>
        <div class="controls-grid">
          <div class="form-group">
            <label for="graph-title" class="form-label">Title</label>
            <input type="text" id="graph-title" class="form-input" placeholder="Enter graph title">
          </div>
          <div class="form-group">
            <label for="graph-description" class="form-label">Description</label>
            <input type="text" id="graph-description" class="form-input" placeholder="Brief description">
          </div>
          <div class="form-group">
            <label for="graph-tags" class="form-label">Tags (comma-separated)</label>
            <input type="text" id="graph-tags" class="form-input" placeholder="network, diagram, flow">
          </div>
        </div>
        
        <div class="button-group">
          <button id="save-btn" class="btn btn-success">💾 Save Graph</button>
          <button id="export-svg-btn" class="btn">📥 Export SVG</button>
          <button id="export-png-btn" class="btn">📥 Export PNG</button>
          <button id="templates-btn" class="btn">📋 Templates</button>
          <button id="clear-btn" class="btn">🗑️ Clear</button>
          <a href="dot-gallery.html" class="btn">📊 View Gallery</a>
        </div>

        <!-- Templates Modal -->
        <div id="templates-modal" class="modal" style="display: none;">
          <div class="modal-content">
            <div class="modal-header">
              <h3>DOT Templates</h3>
              <button id="close-modal" class="btn">✕</button>
            </div>
            <div class="templates-grid">
              <!-- Templates will be populated by JavaScript -->
            </div>
          </div>
        </div>
        
        <div id="message-container"></div>
      </div>
    </main>

    <footer id="page-footer" style="opacity: 1;">
      <p>Maintained by Ctx. Served by http-server.</p>
    </footer>

    <script src="dot-editor.js"></script>
  </body>
</html>
