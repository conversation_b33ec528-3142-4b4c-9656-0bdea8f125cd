/* Modern CSS using Open Props for .gemini/www website */

/* CSS Custom Properties for theming */
:root {
  --primary-color: var(--blue-6);
  --secondary-color: var(--gray-6);
  --accent-color: var(--green-6);
  --text-color: var(--gray-9);
  --text-light: var(--gray-7);
  --bg-color: var(--gray-0);
  --card-bg: var(--gray-1);
  --border-color: var(--gray-3);
  --shadow: var(--shadow-2);
  --radius: var(--radius-2);
}

/* Dark theme */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: var(--blue-4);
    --secondary-color: var(--gray-4);
    --accent-color: var(--green-4);
    --text-color: var(--gray-1);
    --text-light: var(--gray-3);
    --bg-color: var(--gray-9);
    --card-bg: var(--gray-8);
    --border-color: var(--gray-7);
    --shadow: var(--shadow-6);
  }

  .tag {
    background: var(--blue-6);
    color: var(--gray-0);
    border-color: var(--blue-7);
  }
}

/* Base styles */
html {
  /* Modern solution: reserve space for scrollbar to prevent layout shift */
  scrollbar-gutter: stable;
}

* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-sans);
  line-height: var(--font-lineheight-3);
  color: var(--text-color);
  background-color: var(--bg-color);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Typography */
h1 {
  font-size: var(--font-size-6);
  font-weight: var(--font-weight-7);
  color: var(--primary-color);
  margin: 0 0 var(--size-2) 0;
}

h2 {
  font-size: var(--font-size-4);
  font-weight: var(--font-weight-6);
  color: var(--text-color);
  margin: var(--size-4) 0 var(--size-2) 0;
}

p {
  margin: 0 0 var(--size-3) 0;
  color: var(--text-light);
}

/* Header */
header {
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  padding: var(--size-4) var(--size-6);
  text-align: center;
  box-shadow: var(--shadow);
}

/* Main content */
main {
  flex: 1;
  padding: var(--size-6);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Search and controls */
.controls {
  margin-bottom: var(--size-6);
  display: flex;
  gap: var(--size-3);
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.search-box {
  flex: 1;
  min-width: var(--size-13);
  padding: var(--size-2) var(--size-3);
  border: var(--border-size-1) solid var(--border-color);
  border-radius: var(--radius-2);
  background: var(--card-bg);
  color: var(--text-color);
  font-size: var(--font-size-1);
  font-family: var(--font-sans);
  box-shadow: var(--shadow-1);
  transition: var(--animation-fade-in);
}

.search-box:focus {
  outline: none;
  border-color: var(--blue-6);
  box-shadow: var(--shadow-2), 0 0 0 var(--border-size-2) var(--blue-2);
}

.sort-controls {
  display: flex;
  gap: var(--size-2);
  align-items: center;
}

.sort-button {
  padding: var(--size-2) var(--size-3);
  border: var(--border-size-1) solid var(--border-color);
  border-radius: var(--radius-2);
  background: var(--card-bg);
  color: var(--text-color);
  cursor: pointer;
  font-size: var(--font-size-0);
  font-weight: var(--font-weight-5);
  transition: var(--animation-fade-in);
  box-shadow: var(--shadow-1);
}

.sort-button:hover {
  background: var(--blue-6);
  color: var(--gray-0);
  border-color: var(--blue-7);
  box-shadow: var(--shadow-2);
  transform: translateY(var(--size-1));
}

.sort-button.active {
  background: var(--blue-7);
  color: var(--gray-0);
  border-color: var(--blue-8);
  box-shadow: var(--shadow-3);
}

/* Card container */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--size-4);
  margin-top: var(--size-4);
}

/* Document cards */
.document-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: var(--size-4);
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  display: block;
}

.document-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-3);
  border-color: var(--primary-color);
}

.card-title {
  font-size: var(--font-size-2);
  font-weight: var(--font-weight-6);
  color: var(--text-color);
  margin: 0 0 var(--size-2) 0;
  line-height: var(--font-lineheight-2);
}

.card-description {
  font-size: var(--font-size-1);
  color: var(--text-light);
  margin: 0 0 var(--size-3) 0;
  line-height: var(--font-lineheight-3);
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-0);
  color: var(--text-light);
}

.card-date {
  font-weight: var(--font-weight-5);
}

.card-tags {
  display: flex;
  gap: var(--size-1);
  flex-wrap: wrap;
}

.tag {
  background: var(--blue-7);
  color: var(--gray-0);
  padding: var(--size-1) var(--size-2);
  border-radius: var(--radius-2);
  font-size: var(--font-size-0);
  font-weight: var(--font-weight-6);
  border: 1px solid var(--blue-8);
}

/* Loading states */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--size-6);
  color: var(--text-light);
}

.skeleton-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: var(--size-4);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Footer */
footer {
  background: var(--card-bg);
  border-top: 1px solid var(--border-color);
  padding: var(--size-3) var(--size-6);
  text-align: center;
  color: var(--text-light);
  font-size: var(--font-size-0);
}

/* Responsive design */
@media (max-width: 768px) {
  main {
    padding: var(--size-4);
  }
  
  header {
    padding: var(--size-3) var(--size-4);
  }
  
  .controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .card-container {
    grid-template-columns: 1fr;
  }
  
  h1 {
    font-size: var(--font-size-5);
  }
}

@media (max-width: 480px) {
  .card-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--size-2);
  }
  
  .card-tags {
    width: 100%;
  }
}

/* Utility classes */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
