<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graph Gallery | Ctx | Gemini-CLI Dashboard</title>
    <link rel="stylesheet" href="https://unpkg.com/open-props@1.7.4/open-props.min.css">
    <link rel="stylesheet" href="https://unpkg.com/open-props@1.7.4/normalize.min.css">
    <link rel="stylesheet" href="style.css">
    
    <!-- GraphViz WASM Library for thumbnail generation -->
    <script src="https://unpkg.com/@hpcc-js/wasm@2.13.0/dist/graphviz.umd.js"></script>
    
    <style>
      /* Always show vertical scrollbar to prevent layout jank */
      html {
        overflow-y: scroll;
      }
      
      /* Gallery specific styles */
      .gallery-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: var(--size-6);
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .gallery-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--size-6);
        padding-bottom: var(--size-4);
        border-bottom: 1px solid var(--border-color);
      }
      
      .gallery-title {
        font-size: var(--font-size-6);
        font-weight: var(--font-weight-7);
        margin: 0;
        color: var(--text-color);
      }
      
      .gallery-actions {
        display: flex;
        gap: var(--size-3);
        align-items: center;
      }
      
      .search-input {
        padding: var(--size-2) var(--size-3);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-0);
        color: var(--gray-9);
        font-size: var(--font-size-2);
        outline: none;
        width: 250px;
        transition: border-color 0.2s ease;
      }
      
      .search-input:focus {
        border-color: var(--blue-7);
        box-shadow: 0 0 0 2px var(--blue-2);
      }
      
      .filter-select {
        padding: var(--size-2);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-0);
        color: var(--gray-9);
        font-size: var(--font-size-2);
        outline: none;
        cursor: pointer;
      }
      
      .btn {
        padding: var(--size-2) var(--size-4);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-1);
        color: var(--gray-9);
        font-size: var(--font-size-2);
        font-weight: var(--font-weight-5);
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--size-1);
      }
      
      .btn:hover {
        background: var(--gray-2);
        border-color: var(--gray-6);
      }
      
      .btn-primary {
        background: var(--blue-7);
        color: var(--gray-0);
        border-color: var(--blue-8);
      }
      
      .btn-primary:hover {
        background: var(--blue-8);
        border-color: var(--blue-9);
      }
      
      .graphs-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: var(--size-5);
        margin-bottom: var(--size-6);
      }
      
      .graph-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        overflow: hidden;
        box-shadow: var(--shadow);
        transition: all 0.2s ease;
        cursor: pointer;
      }
      
      .graph-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-3);
        border-color: var(--blue-6);
      }
      
      .graph-thumbnail {
        width: 100%;
        height: 200px;
        background: var(--gray-0);
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        position: relative;
      }
      
      .thumbnail-svg {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
      
      .thumbnail-placeholder {
        color: var(--text-light);
        font-style: italic;
        text-align: center;
        padding: var(--size-4);
      }
      
      .graph-info {
        padding: var(--size-4);
      }
      
      .graph-title {
        font-size: var(--font-size-3);
        font-weight: var(--font-weight-6);
        margin: 0 0 var(--size-2) 0;
        color: var(--text-color);
        line-height: var(--font-lineheight-2);
      }
      
      .graph-description {
        font-size: var(--font-size-1);
        color: var(--text-light);
        margin: 0 0 var(--size-3) 0;
        line-height: var(--font-lineheight-3);
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      
      .graph-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: var(--font-size-0);
        color: var(--text-light);
        margin-bottom: var(--size-3);
      }
      
      .graph-engine {
        background: var(--blue-2);
        color: var(--blue-9);
        padding: var(--size-1) var(--size-2);
        border-radius: var(--radius-1);
        font-weight: var(--font-weight-5);
      }
      
      .graph-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--size-1);
      }
      
      .tag {
        background: var(--gray-2);
        color: var(--gray-8);
        padding: var(--size-1) var(--size-2);
        border-radius: var(--radius-1);
        font-size: var(--font-size-0);
        font-weight: var(--font-weight-5);
      }
      
      .graph-actions {
        display: flex;
        gap: var(--size-2);
        padding-top: var(--size-3);
        border-top: 1px solid var(--border-color);
      }
      
      .action-btn {
        flex: 1;
        padding: var(--size-2);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-1);
        background: var(--gray-1);
        color: var(--gray-9);
        font-size: var(--font-size-1);
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
        text-decoration: none;
      }
      
      .action-btn:hover {
        background: var(--gray-2);
      }
      
      .action-btn.view {
        background: var(--blue-7);
        color: var(--gray-0);
        border-color: var(--blue-8);
      }
      
      .action-btn.view:hover {
        background: var(--blue-8);
      }
      
      .empty-state {
        text-align: center;
        padding: var(--size-8);
        color: var(--text-light);
      }
      
      .empty-state h3 {
        font-size: var(--font-size-4);
        margin-bottom: var(--size-3);
      }
      
      .empty-state p {
        font-size: var(--font-size-2);
        margin-bottom: var(--size-4);
      }
      
      .loading-state {
        text-align: center;
        padding: var(--size-8);
        color: var(--text-light);
      }
      
      .loading-spinner {
        display: inline-block;
        width: 40px;
        height: 40px;
        border: 4px solid var(--gray-3);
        border-radius: 50%;
        border-top-color: var(--blue-7);
        animation: spin 1s ease-in-out infinite;
        margin-bottom: var(--size-3);
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
        .search-input, .filter-select {
          background: var(--gray-8);
          color: var(--gray-1);
        }
        
        .graph-thumbnail {
          background: var(--gray-8);
        }
        
        .graph-engine {
          background: var(--blue-8);
          color: var(--blue-1);
        }
        
        .tag {
          background: var(--gray-7);
          color: var(--gray-2);
        }
      }
      
      /* Responsive design */
      @media (max-width: 768px) {
        .gallery-header {
          flex-direction: column;
          gap: var(--size-4);
          align-items: stretch;
        }
        
        .gallery-actions {
          flex-direction: column;
        }
        
        .search-input {
          width: 100%;
        }
        
        .graphs-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="header-content">
        <div class="header-left">
          <h1 class="site-title">
            <a href="index.html">Ctx | Gemini-CLI Dashboard</a>
          </h1>
          <span class="page-subtitle">Graph Gallery</span>
        </div>
        <nav class="header-nav">
          <a href="index.html" class="nav-link">📄 Documents</a>
          <a href="dot-gallery.html" class="nav-link active">📊 Graph Gallery</a>
          <a href="dot-editor.html" class="nav-link">✏️ DOT Editor</a>
        </nav>
      </div>
    </header>

    <main class="gallery-container">
      <div class="gallery-header">
        <h2 class="gallery-title">Graph Gallery</h2>
        <div class="gallery-actions">
          <input type="text" id="search-input" class="search-input" placeholder="Search graphs...">
          <select id="filter-select" class="filter-select">
            <option value="">All Tags</option>
          </select>
          <a href="dot-editor.html" class="btn btn-primary">✏️ New Graph</a>
        </div>
      </div>

      <div id="loading-state" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Loading graphs...</p>
      </div>

      <div id="graphs-grid" class="graphs-grid" style="display: none;">
        <!-- Graph cards will be populated by JavaScript -->
      </div>

      <div id="empty-state" class="empty-state" style="display: none;">
        <h3>No Graphs Found</h3>
        <p>Create your first graph to get started!</p>
        <a href="dot-editor.html" class="btn btn-primary">✏️ Create Graph</a>
      </div>
    </main>

    <footer id="page-footer" style="opacity: 1;">
      <p>Maintained by Ctx. Served by http-server.</p>
    </footer>

    <script src="dot-gallery.js"></script>
  </body>
</html>
