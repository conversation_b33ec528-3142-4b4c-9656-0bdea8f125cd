class DOTEditor {
    constructor() {
        this.graphviz = null;
        this.currentGraph = null;
        this.isRendering = false;
        
        this.initializeElements();
        this.setupEventListeners();
        this.initializeGraphviz();
        this.loadExampleGraph();
    }

    initializeElements() {
        this.dotInput = document.getElementById('dot-input');
        this.engineSelect = document.getElementById('engine-select');
        this.previewContainer = document.getElementById('preview-container');
        this.renderBtn = document.getElementById('render-btn');
        this.saveBtn = document.getElementById('save-btn');
        this.exportSvgBtn = document.getElementById('export-svg-btn');
        this.exportPngBtn = document.getElementById('export-png-btn');
        this.clearBtn = document.getElementById('clear-btn');
        this.titleInput = document.getElementById('graph-title');
        this.descriptionInput = document.getElementById('graph-description');
        this.tagsInput = document.getElementById('graph-tags');
        this.messageContainer = document.getElementById('message-container');
    }

    setupEventListeners() {
        this.renderBtn.addEventListener('click', () => this.renderGraph());
        this.saveBtn.addEventListener('click', () => this.saveGraph());
        this.exportSvgBtn.addEventListener('click', () => this.exportSVG());
        this.exportPngBtn.addEventListener('click', () => this.exportPNG());
        this.clearBtn.addEventListener('click', () => this.clearEditor());
        
        // Auto-render on engine change
        this.engineSelect.addEventListener('change', () => this.renderGraph());
        
        // Auto-render on input (with debounce)
        let renderTimeout;
        this.dotInput.addEventListener('input', () => {
            clearTimeout(renderTimeout);
            renderTimeout = setTimeout(() => this.renderGraph(), 1000);
        });
    }

    async initializeGraphviz() {
        try {
            this.showMessage('Initializing GraphViz...', 'info');
            this.graphviz = await window['@hpcc-js/wasm'].Graphviz.load();
            this.showMessage('GraphViz loaded successfully!', 'success');
            this.renderBtn.disabled = false;
        } catch (error) {
            console.error('Failed to initialize GraphViz:', error);
            this.showMessage('Failed to load GraphViz. Please refresh the page.', 'error');
        }
    }

    loadExampleGraph() {
        const exampleDOT = `digraph example {
    rankdir=LR;
    node [shape=box, style=rounded];
    
    start [label="Start", shape=ellipse, style=filled, fillcolor=lightgreen];
    process1 [label="Process Data"];
    decision [label="Valid?", shape=diamond, style=filled, fillcolor=lightyellow];
    process2 [label="Save Result"];
    end [label="End", shape=ellipse, style=filled, fillcolor=lightcoral];
    
    start -> process1;
    process1 -> decision;
    decision -> process2 [label="Yes"];
    decision -> end [label="No"];
    process2 -> end;
}`;
        
        this.dotInput.value = exampleDOT;
        this.titleInput.value = 'Example Process Flow';
        this.descriptionInput.value = 'A simple process flow diagram';
        this.tagsInput.value = 'example, process, flow';
    }

    async renderGraph() {
        if (!this.graphviz || this.isRendering) return;
        
        const dotCode = this.dotInput.value.trim();
        if (!dotCode) {
            this.previewContainer.innerHTML = '<div class="preview-placeholder">Enter DOT code to see preview</div>';
            return;
        }

        this.isRendering = true;
        this.renderBtn.textContent = '⏳ Rendering...';
        this.renderBtn.disabled = true;

        try {
            const engine = this.engineSelect.value;
            const svg = this.graphviz.layout(dotCode, "svg", engine);
            
            this.previewContainer.innerHTML = svg;
            this.currentGraph = { dotCode, engine, svg };
            this.showMessage('Graph rendered successfully!', 'success');
            
        } catch (error) {
            console.error('Render error:', error);
            this.previewContainer.innerHTML = `
                <div class="error-message">
                    <strong>Render Error:</strong><br>
                    ${this.escapeHtml(error.message)}
                </div>
            `;
            this.showMessage('Failed to render graph. Check your DOT syntax.', 'error');
        } finally {
            this.isRendering = false;
            this.renderBtn.textContent = '🔄 Render';
            this.renderBtn.disabled = false;
        }
    }

    async saveGraph() {
        if (!this.currentGraph) {
            this.showMessage('Please render a graph first before saving.', 'error');
            return;
        }

        const title = this.titleInput.value.trim();
        const description = this.descriptionInput.value.trim();
        const tags = this.tagsInput.value.trim();

        if (!title) {
            this.showMessage('Please enter a title for the graph.', 'error');
            return;
        }

        try {
            // Create graph metadata
            const graphData = {
                id: this.generateId(),
                fileName: this.sanitizeFileName(title) + '.dot',
                title: title,
                description: description,
                tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
                engine: this.currentGraph.engine,
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                toBeArchived: false,
                dotCode: this.currentGraph.dotCode
            };

            // For now, we'll simulate saving by storing in localStorage
            // In a real implementation, this would save to the server
            this.saveToLocalStorage(graphData);
            
            this.showMessage(`Graph "${title}" saved successfully!`, 'success');
            
        } catch (error) {
            console.error('Save error:', error);
            this.showMessage('Failed to save graph. Please try again.', 'error');
        }
    }

    saveToLocalStorage(graphData) {
        // Get existing graphs
        const existingGraphs = JSON.parse(localStorage.getItem('dotGraphs') || '[]');
        
        // Add new graph
        existingGraphs.push(graphData);
        
        // Save back to localStorage
        localStorage.setItem('dotGraphs', JSON.stringify(existingGraphs));
    }

    exportSVG() {
        if (!this.currentGraph) {
            this.showMessage('Please render a graph first before exporting.', 'error');
            return;
        }

        const title = this.titleInput.value.trim() || 'graph';
        const blob = new Blob([this.currentGraph.svg], { type: 'image/svg+xml' });
        this.downloadBlob(blob, `${this.sanitizeFileName(title)}.svg`);
        this.showMessage('SVG exported successfully!', 'success');
    }

    exportPNG() {
        if (!this.currentGraph) {
            this.showMessage('Please render a graph first before exporting.', 'error');
            return;
        }

        const title = this.titleInput.value.trim() || 'graph';
        const svgElement = this.previewContainer.querySelector('svg');
        
        if (!svgElement) {
            this.showMessage('No SVG found to export.', 'error');
            return;
        }

        // Create canvas and convert SVG to PNG
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            canvas.toBlob((blob) => {
                this.downloadBlob(blob, `${this.sanitizeFileName(title)}.png`);
                this.showMessage('PNG exported successfully!', 'success');
            }, 'image/png');
        };
        
        img.onerror = () => {
            this.showMessage('Failed to export PNG. Try SVG export instead.', 'error');
        };
        
        const svgData = new XMLSerializer().serializeToString(svgElement);
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(svgBlob);
        img.src = url;
    }

    clearEditor() {
        if (confirm('Are you sure you want to clear the editor? This will remove all current work.')) {
            this.dotInput.value = '';
            this.titleInput.value = '';
            this.descriptionInput.value = '';
            this.tagsInput.value = '';
            this.previewContainer.innerHTML = '<div class="preview-placeholder">Enter DOT code to see preview</div>';
            this.currentGraph = null;
            this.showMessage('Editor cleared.', 'info');
        }
    }

    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    generateId() {
        return 'graph-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    sanitizeFileName(name) {
        return name.toLowerCase()
                  .replace(/[^a-z0-9]/g, '-')
                  .replace(/-+/g, '-')
                  .replace(/^-|-$/g, '');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `${type}-message`;
        messageDiv.textContent = message;
        
        this.messageContainer.innerHTML = '';
        this.messageContainer.appendChild(messageDiv);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (this.messageContainer.contains(messageDiv)) {
                this.messageContainer.removeChild(messageDiv);
            }
        }, 5000);
    }
}

// Initialize the DOT Editor when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new DOTEditor();
});
