<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document Viewer | Ctx Gemini-CLI Dashboard</title>

    <!-- Open Props -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/open-props@1.6.2/open-props.min.css"
    />
    <!-- Your custom styles -->
    <link rel="stylesheet" href="style.css" />
    <!-- Markdown-it for rendering -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <!-- Highlight.js for code syntax highlighting -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/lib/highlight.min.js"></script>
    
    <style>
      /* Additional styles for document viewer */
      .viewer-container {
        max-width: 900px;
        margin: 0 auto;
        padding: var(--size-6);
      }
      
      .document-header {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        padding: var(--size-4);
        margin-bottom: var(--size-6);
        box-shadow: var(--shadow);
      }
      
      .document-title {
        font-size: var(--font-size-5);
        font-weight: var(--font-weight-7);
        color: var(--primary-color);
        margin: 0 0 var(--size-2) 0;
      }
      
      .document-meta {
        display: flex;
        gap: var(--size-4);
        align-items: center;
        font-size: var(--font-size-1);
        color: var(--text-light);
        margin-bottom: var(--size-3);
      }
      
      .document-description {
        font-size: var(--font-size-2);
        color: var(--text-color);
        margin: 0;
      }
      
      .navigation {
        display: flex;
        gap: var(--size-3);
        margin-bottom: var(--size-4);
      }
      
      .nav-button {
        padding: var(--size-2) var(--size-3);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        background: var(--card-bg);
        color: var(--text-color);
        text-decoration: none;
        font-size: var(--font-size-1);
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: var(--size-1);
      }
      
      .nav-button:hover {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
      }
      
      .document-content {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius);
        padding: var(--size-6);
        box-shadow: var(--shadow);
        line-height: var(--font-lineheight-4);
      }
      
      .document-content h1,
      .document-content h2,
      .document-content h3,
      .document-content h4,
      .document-content h5,
      .document-content h6 {
        color: var(--text-color);
        margin-top: var(--size-5);
        margin-bottom: var(--size-3);
      }
      
      .document-content h1 {
        font-size: var(--font-size-4);
        border-bottom: 2px solid var(--border-color);
        padding-bottom: var(--size-2);
      }
      
      .document-content h2 {
        font-size: var(--font-size-3);
      }
      
      .document-content p {
        margin-bottom: var(--size-3);
        color: var(--text-color);
      }
      
      .document-content ul,
      .document-content ol {
        margin-bottom: var(--size-3);
        padding-left: var(--size-5);
      }
      
      .document-content li {
        margin-bottom: var(--size-1);
        color: var(--text-color);
      }
      
      .document-content code {
        background: var(--gray-2);
        color: var(--gray-9);
        padding: var(--size-1);
        border-radius: var(--radius-1);
        font-family: var(--font-mono);
        font-size: var(--font-size-0);
      }

      .tag {
        background: var(--blue-7);
        color: var(--gray-0);
        padding: var(--size-1) var(--size-2);
        border-radius: var(--radius-2);
        font-size: var(--font-size-0);
        font-weight: var(--font-weight-6);
        border: 1px solid var(--blue-8);
        display: inline-block;
        margin-right: var(--size-1);
        margin-bottom: var(--size-1);
      }

      .status-tag {
        background: var(--orange-7) !important;
        color: var(--gray-0) !important;
        border: 1px solid var(--orange-8) !important;
        font-weight: var(--font-weight-6) !important;
      }

      @media (prefers-color-scheme: dark) {
        .document-content code {
          background: var(--gray-7);
          color: var(--gray-1);
        }

        .status-tag {
          background: var(--orange-6) !important;
          color: var(--gray-0) !important;
          border-color: var(--orange-7) !important;
        }
      }
      
      .document-content pre {
        background: var(--gray-2);
        padding: var(--size-3);
        border-radius: var(--radius);
        overflow-x: auto;
        margin-bottom: var(--size-3);
      }
      
      .document-content pre code {
        background: none;
        padding: 0;
      }
      
      .document-content table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: var(--size-3);
      }
      
      .document-content th,
      .document-content td {
        border: 1px solid var(--border-color);
        padding: var(--size-2);
        text-align: left;
      }
      
      .document-content th {
        background: var(--gray-2);
        font-weight: var(--font-weight-6);
      }
      
      .document-content blockquote {
        border-left: 4px solid var(--primary-color);
        padding-left: var(--size-3);
        margin: var(--size-3) 0;
        color: var(--text-light);
        font-style: italic;
      }
      
      .error-message {
        background: var(--red-1);
        border: 1px solid var(--red-3);
        color: var(--red-9);
        padding: var(--size-4);
        border-radius: var(--radius);
        margin: var(--size-4) 0;
      }
      
      .loading {
        text-align: center;
        padding: var(--size-6);
        color: var(--text-light);
      }
      
      @media (max-width: 768px) {
        .viewer-container {
          padding: var(--size-4);
        }
        
        .document-meta {
          flex-direction: column;
          align-items: flex-start;
          gap: var(--size-2);
        }
        
        .navigation {
          flex-wrap: wrap;
        }
        
        .document-content {
          padding: var(--size-4);
        }
      }
    </style>
  </head>
  <body>
    <header>
      <h1>Document Viewer</h1>
      <p>Ctx | Gemini-CLI Dashboard</p>
    </header>

    <main class="viewer-container">
      <div class="navigation">
        <a href="index.html" class="nav-button">← Back to Documents</a>
      </div>
      
      <div class="document-header" id="document-header">
        <div class="loading">Loading document...</div>
      </div>
      
      <div class="document-content" id="document-content">
        <div class="loading">Loading content...</div>
      </div>
    </main>

    <footer>
      <p>Maintained by Ctx. Served by http-server.</p>
    </footer>

    <script src="viewer.js"></script>
  </body>
</html>
