// Document viewer JavaScript for .gemini/www website
// Handles document loading, markdown rendering, and navigation

class DocumentViewer {
    constructor() {
        this.md = window.markdownit({
            html: true,
            linkify: true,
            typographer: true,
            breaks: true
        });
        
        // Enable table support
        this.md.enable(['table']);
        
        this.currentDocument = null;
        this.allDocuments = [];
        this.init();
    }

    async init() {
        try {
            const docParam = this.getDocumentFromURL();
            if (!docParam) {
                this.showError('No document specified in URL');
                return;
            }

            await this.loadManifest();
            await this.loadDocument(docParam);
            this.setupNavigation();
        } catch (error) {
            console.error('Failed to initialize DocumentViewer:', error);
            this.showError('Failed to load document. Please check the URL and try again.');
        }
    }

    getDocumentFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('doc');
    }

    async loadManifest() {
        try {
            const response = await fetch('./manifest.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const manifest = await response.json();
            
            if (!manifest.documents || !Array.isArray(manifest.documents)) {
                throw new Error('Invalid manifest format');
            }
            
            this.allDocuments = manifest.documents.filter(doc => !doc.toBeArchived);
        } catch (error) {
            console.error('Error loading manifest:', error);
            throw error;
        }
    }

    async loadDocument(fileName) {
        try {
            // Find document in manifest
            const docInfo = this.allDocuments.find(doc => doc.fileName === fileName);
            if (!docInfo) {
                throw new Error(`Document ${fileName} not found in manifest`);
            }

            // Load document content
            const response = await fetch(`./docs/${fileName}`);
            if (!response.ok) {
                throw new Error(`Failed to load document: ${response.status}`);
            }
            
            const content = await response.text();
            const metadata = this.extractMetadata(content, fileName);
            
            this.currentDocument = { ...docInfo, ...metadata, content };
            
            this.renderDocument();
            this.updatePageTitle();
            
        } catch (error) {
            console.error('Error loading document:', error);
            this.showError(`Failed to load document: ${error.message}`);
        }
    }

    extractMetadata(content, fileName) {
        const lines = content.split('\n');
        let title = '';
        let description = '';
        let status = '';
        
        // Extract title from first heading
        for (const line of lines) {
            if (line.startsWith('# ')) {
                title = line.substring(2).trim();
                break;
            }
        }
        
        // Extract status if present
        const statusMatch = content.match(/\*\*STATUS:\*\*\s*(.+)/i);
        if (statusMatch) {
            status = statusMatch[1].trim();
        }
        
        // Extract description from first paragraph after title
        let foundTitle = false;
        for (const line of lines) {
            if (line.startsWith('# ')) {
                foundTitle = true;
                continue;
            }
            if (foundTitle && line.trim() && !line.startsWith('**') && !line.startsWith('---')) {
                description = line.trim();
                break;
            }
        }
        
        return {
            title: title || this.generateTitleFromFileName(fileName),
            description: description || 'No description available',
            date: this.extractDateFromFileName(fileName) || new Date().toISOString().split('T')[0],
            status: status,
            tags: this.extractTagsFromContent(content)
        };
    }

    generateTitleFromFileName(fileName) {
        return fileName
            .replace(/\.md$/, '')
            .replace(/checklist-\d{8}-\d{6}-/, '')
            .replace(/-/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
    }

    extractDateFromFileName(fileName) {
        const dateMatch = fileName.match(/(\d{4})(\d{2})(\d{2})/);
        if (dateMatch) {
            return `${dateMatch[1]}-${dateMatch[2]}-${dateMatch[3]}`;
        }
        return null;
    }

    extractTagsFromContent(content) {
        const tags = [];
        
        // Look for common patterns that might indicate tags
        if (content.includes('checklist')) tags.push('checklist');
        if (content.includes('neo4j')) tags.push('neo4j');
        if (content.includes('migration')) tags.push('migration');
        if (content.includes('refactor')) tags.push('refactor');
        if (content.includes('testing')) tags.push('testing');
        if (content.includes('STATUS: PENDING')) tags.push('pending');
        if (content.includes('STATUS: COMPLETE')) tags.push('complete');
        
        return tags;
    }

    renderDocument() {
        if (!this.currentDocument) return;

        this.renderHeader();
        this.renderContent();
    }

    renderHeader() {
        const headerElement = document.getElementById('document-header');
        if (!headerElement) return;

        const tags = this.currentDocument.tags && this.currentDocument.tags.length > 0 
            ? this.currentDocument.tags.map(tag => `<span class="tag">${tag}</span>`).join('')
            : '';

        const statusBadge = this.currentDocument.status
            ? `<span class="tag status-tag">${this.currentDocument.status}</span>`
            : '';

        headerElement.innerHTML = `
            <h1 class="document-title">${this.escapeHtml(this.currentDocument.title)}</h1>
            <div class="document-meta">
                <span class="document-date">📅 ${this.currentDocument.date}</span>
                <span class="document-file">📄 ${this.currentDocument.fileName}</span>
                ${statusBadge}
            </div>
            <p class="document-description">${this.escapeHtml(this.currentDocument.description)}</p>
            ${tags ? `<div class="card-tags" style="margin-top: var(--size-3);">${tags}</div>` : ''}
        `;
    }

    renderContent() {
        const contentElement = document.getElementById('document-content');
        if (!contentElement || !this.currentDocument) return;

        try {
            const htmlContent = this.md.render(this.currentDocument.content);
            contentElement.innerHTML = htmlContent;
            
            // Highlight code blocks
            if (window.hljs) {
                contentElement.querySelectorAll('pre code').forEach((block) => {
                    window.hljs.highlightElement(block);
                });
            }
        } catch (error) {
            console.error('Error rendering markdown:', error);
            contentElement.innerHTML = `
                <div class="error-message">
                    <p>⚠️ Error rendering document content: ${error.message}</p>
                    <details>
                        <summary>Raw content</summary>
                        <pre>${this.escapeHtml(this.currentDocument.content)}</pre>
                    </details>
                </div>
            `;
        }
    }

    setupNavigation() {
        // Find current document index
        const currentIndex = this.allDocuments.findIndex(doc => 
            doc.fileName === this.currentDocument.fileName
        );
        
        if (currentIndex === -1) return;

        const navigation = document.querySelector('.navigation');
        if (!navigation) return;

        // Add previous/next buttons
        let navButtons = '<a href="index.html" class="nav-button">← Back to Documents</a>';
        
        if (currentIndex > 0) {
            const prevDoc = this.allDocuments[currentIndex - 1];
            navButtons += `<a href="viewer.html?doc=${encodeURIComponent(prevDoc.fileName)}" class="nav-button">← Previous</a>`;
        }
        
        if (currentIndex < this.allDocuments.length - 1) {
            const nextDoc = this.allDocuments[currentIndex + 1];
            navButtons += `<a href="viewer.html?doc=${encodeURIComponent(nextDoc.fileName)}" class="nav-button">Next →</a>`;
        }
        
        navigation.innerHTML = navButtons;
    }

    updatePageTitle() {
        if (this.currentDocument && this.currentDocument.title) {
            document.title = `${this.currentDocument.title} | Ctx Gemini-CLI Dashboard`;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showError(message) {
        const headerElement = document.getElementById('document-header');
        const contentElement = document.getElementById('document-content');
        
        if (headerElement) {
            headerElement.innerHTML = `
                <div class="error-message">
                    <h2>⚠️ Error</h2>
                    <p>${message}</p>
                </div>
            `;
        }
        
        if (contentElement) {
            contentElement.innerHTML = `
                <div class="error-message">
                    <p>Unable to load document content.</p>
                    <a href="index.html" class="nav-button">← Return to Documents</a>
                </div>
            `;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DocumentViewer();
});
